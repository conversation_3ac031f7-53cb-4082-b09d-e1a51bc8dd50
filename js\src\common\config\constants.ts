/**
 * Application constants to eliminate magic numbers
 */

// Mobile detection constants
export const M<PERSON><PERSON><PERSON>_DETECTION = {
  USER_AGENT_SUBSTR_START: 0,
  USER_AGENT_SUBSTR_LENGTH: 4,
} as const;

// Swiper configuration constants
export const SWIPER_CONFIG = {
  MOBILE: {
    SPACE_BETWEEN: 90,
    SLIDES_PER_VIEW: 2,
    TAG_SPACE_BETWEEN: 80,
    TAG_SLIDES_PER_VIEW: 4,
  },
  DESKTOP: {
    SPACE_BETWEEN: 10,
    SLIDES_PER_VIEW: 7,
    TAG_SPACE_BETWEEN: 10,
    TAG_SLIDES_PER_VIEW: 7,
  },
  AUTOPLAY_DELAY: 3000,
} as const;

// Error handling constants
export const ERROR_HANDLING = {
  MAX_ERROR_LOG_ENTRIES: 50,
  DOM_READY_TIMEOUT: 5000,
  SLIDE_NUMBER_MIN: 1,
  SLIDE_NUMBER_MAX: 30,
  TRANSITION_TIME_MIN: 1000,
  TRANSITION_TIME_MAX: 30_000,
  CONFIG_MAX_SLIDES_MIN: 1,
  CONFIG_MAX_SLIDES_MAX: 50,
} as const;

// UI styling constants
export const UI_STYLES = {
  HEADER_ICON_HEIGHT: 24,
  HEADER_ICON_MARGIN_TOP: 8,
  MOBILE_BUTTON_FONT_SIZE: 14,
  MOBILE_BUTTON_WORD_SPACING: -1,
  SOCIAL_ICON_WIDTH: 32,
  SOCIAL_ICON_MARGIN_LEFT: 20,
  TAG_TEXT_FONT_SIZE: 14,
  TAG_CONTAINER_PADDING_TOP: 10,
  TAG_CONTAINER_MARGIN_TOP: 5,
} as const;

// Mobile layout constants
export const MOBILE_LAYOUT = {
  SCREEN_WIDTH_MULTIPLIER: 2,
  SCREEN_WIDTH_OFFSET: 50,
  CONTAINER_MARGIN_MULTIPLIER: 0.254,
} as const;

// Timing constants
export const TIMING = {
  CHECK_INTERVAL: 10,
  DATA_CHECK_INTERVAL: 100,
  DEFAULT_TRANSITION_TIME: 5000,
} as const;

// DOM element constants
export const DOM_ELEMENTS = {
  SWIPER_AD_CONTAINER_ID: 'swiperAdContainer',
  SWIPER_TAG_CONTAINER_ID: 'swiperTagContainer',
  SWIPER_TAG_WRAPPER_ID: 'swiperTagWrapper',
  HEADER_ICON_ID: 'wusong8899Client1HeaderIcon',
} as const;

// CSS class constants
export const CSS_CLASSES = {
  SWIPER: 'swiper',
  SWIPER_WRAPPER: 'swiper-wrapper',
  SWIPER_SLIDE: 'swiper-slide',
  SWIPER_SLIDE_TAG: 'swiper-slide-tag',
  SWIPER_SLIDE_TAG_INNER: 'swiper-slide-tag-inner',
  SWIPER_SLIDE_TAG_INNER_MOBILE: 'swiper-slide-tag-inner-mobile',
  SWIPER_BUTTON_NEXT: 'swiper-button-next',
  SWIPER_BUTTON_PREV: 'swiper-button-prev',
  SWIPER_PAGINATION: 'swiper-pagination',
  AD_SWIPER: 'adSwiper',
  TAG_SWIPER: 'tagSwiper',
  TAG_TILES: 'TagTiles',
  TAG_TILE: 'TagTile',
  TAG_TILE_NAME: 'TagTile-name',
  TAG_TILE_DESCRIPTION: 'TagTile-description',
  TAG_TEXT_OUTER_CONTAINER: 'TagTextOuterContainer',
  TAG_TEXT_CONTAINER: 'TagTextContainer',
  TAG_TEXT_ICON: 'TagTextIcon',
  BUTTON_REGISTER: 'buttonRegister',
} as const;

// CSS selector constants
export const CSS_SELECTORS = {
  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',
  CONTENT_CONTAINER: '#content .container',
  TAGS_PAGE_CONTENT: '#content .container .TagsPage-content',
  NEW_DISCUSSION_BUTTON: '.item-newDiscussion .Button-label',
  NEW_DISCUSSION_ICON: '.item-newDiscussion i',
  NAV_ITEMS: '.item-nav',
  APP_CONTENT: '.App-content',
  SWIPER_PAGINATION_EL: '.swiper-pagination',
  SWIPER_BUTTON_NEXT_EL: '.swiper-button-next',
  SWIPER_BUTTON_PREV_EL: '.swiper-button-prev',
} as const;

// Extension configuration constants
export const EXTENSION_CONFIG = {
  ID: 'wusong8899-client1-header-adv',
  TRANSLATION_PREFIX: 'wusong8899-client1',
  MAX_SLIDES: 30,
  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',
} as const;

// Social media platform constants
export const SOCIAL_PLATFORMS = [
  'Kick',
  'Facebook',
  'Twitter',
  'YouTube',
  'Instagram'
] as const;

export type SocialPlatform = typeof SOCIAL_PLATFORMS[number];
