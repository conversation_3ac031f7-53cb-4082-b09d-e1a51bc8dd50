import app from 'flarum/forum/app';
import { ConfigManager } from './ConfigManager';
import type {
    ErrorLogEntry,
    ValidationResult,
    DependencyCheckResult,
    ConfigurationCheckResult,
    NotificationType
} from '../../common/config/types';
import { ERROR_HANDLING } from '../../common/config/constants';

/**
 * Error handling and logging utility
 */
export class ErrorHandler {
    private static instance: ErrorHandler;
    private configManager: ConfigManager;
    private errorLog: ErrorLogEntry[] = [];

    private constructor() {
        this.configManager = ConfigManager.getInstance();
        this.setupGlobalErrorHandler();
    }

    /**
     * Get singleton instance
     */
    static getInstance(): ErrorHandler {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }

    /**
     * Setup global error handler
     */
    private setupGlobalErrorHandler(): void {
        globalThis.addEventListener('error', (event) => {
            this.logError(event.error, 'Global Error Handler');
        });

        globalThis.addEventListener('unhandledrejection', (event) => {
            this.logError(new Error(event.reason), 'Unhandled Promise Rejection');
        });
    }

    /**
     * Log error with context
     * @param {Error} error - Error object
     * @param {string} context - Error context
     */
    logError(error: Error, context: string = 'Unknown'): void {
        const errorEntry = {
            timestamp: new Date(),
            error,
            context
        };

        this.errorLog.push(errorEntry);

        // Keep only last 50 errors to prevent memory issues
        if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {
            this.errorLog.shift();
        }

        // Log error to internal log only
    }

    /**
     * Handle async operation with error catching
     * @param operation - Async operation to execute
     * @param context - Operation context
     * @param fallbackValue - Value to return on error
     * @returns Operation result or fallback value
     */
    async handleAsync<T>(
        operation: () => Promise<T>,
        context: string,
        fallbackValue: T | null = null
    ): Promise<T | null> {
        try {
            return await operation();
        } catch (error) {
            this.logError(error as Error, context);
            return fallbackValue;
        }
    }

    /**
     * Handle synchronous operation with error catching
     * @param operation - Operation to execute
     * @param context - Operation context
     * @param fallbackValue - Value to return on error
     * @returns Operation result or fallback value
     */
    handleSync<T>(
        operation: () => T,
        context: string,
        fallbackValue: T | null = null
    ): T | null {
        try {
            return operation();
        } catch (error) {
            this.logError(error as Error, context);
            return fallbackValue;
        }
    }

    /**
     * Validate URL format
     * @param url - URL to validate
     * @returns True if valid URL
     */
    isValidUrl(url: string): boolean {
        if (!url || typeof url !== 'string') {
            return false;
        }

        try {
            const urlObj = new URL(url);
            return Boolean(urlObj);
        } catch {
            return false;
        }
    }

    /**
     * Validate slide configuration
     * @param slideNumber - Slide number
     * @param imageUrl - Image URL
     * @param linkUrl - Link URL
     * @returns Validation result
     */
    validateSlideConfig(slideNumber: number, imageUrl: string, linkUrl: string): ValidationResult {
        const errors: string[] = [];

        if (!Number.isInteger(slideNumber) ||
            slideNumber < ERROR_HANDLING.SLIDE_NUMBER_MIN ||
            slideNumber > ERROR_HANDLING.SLIDE_NUMBER_MAX) {
            errors.push(`Slide number must be between ${ERROR_HANDLING.SLIDE_NUMBER_MIN} and ${ERROR_HANDLING.SLIDE_NUMBER_MAX}`);
        }

        if (!imageUrl || !this.isValidUrl(imageUrl)) {
            errors.push('Image URL is required and must be valid');
        }

        if (linkUrl && !this.isValidUrl(linkUrl)) {
            errors.push('Link URL must be valid if provided');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Validate DOM element exists
     * @param selector - CSS selector
     * @param context - Context for error reporting
     * @returns Element if found, null otherwise
     */
    validateElement(selector: string, context = 'Element validation'): Element | null {
        try {
            const element = document.querySelector(selector);
            if (!element) {
                this.logError(new Error(`Element not found: ${selector}`), context);
            }
            return element;
        } catch (error) {
            this.logError(error as Error, context);
            return null;
        }
    }

    /**
     * Safely execute DOM operation
     * @param operation - DOM operation
     * @param context - Operation context
     * @returns Operation result or null
     */
    safeDOMOperation<TResult>(operation: () => TResult, context: string): TResult | null {
        return this.handleSync(operation, `DOM Operation: ${context}`);
    }

    /**
     * Create error notification
     * @param message - Error message
     * @param notificationType - Notification type
     */
    showErrorNotification(message: string, notificationType: NotificationType = 'error'): void {
        try {
            // Use Flarum's notification system if available
            if (app.alerts && app.alerts.show) {
                app.alerts.show({
                    type: notificationType,
                    message
                });
            } else {
                // Silently handle notification fallback
            }
        } catch {
            // Silently handle notification errors
        }
    }

    /**
     * Get error log
     * @returns Array of error entries
     */
    getErrorLog(): ErrorLogEntry[] {
        return [...this.errorLog];
    }

    /**
     * Clear error log
     */
    clearErrorLog(): void {
        this.errorLog = [];
    }

    /**
     * Export error log as JSON
     * @returns {string} JSON string of error log
     */
    exportErrorLog(): string {
        const exportData = this.errorLog.map(entry => ({
            timestamp: entry.timestamp.toISOString(),
            message: entry.error.message,
            stack: entry.error.stack,
            context: entry.context
        }));

        return JSON.stringify(exportData, null, 2);
    }

    /**
     * Check if extension dependencies are available
     * @returns Dependency check results
     */
    checkDependencies(): DependencyCheckResult {
        const missing: string[] = [];

        // Check for required globals
        if (typeof app === 'undefined') {
            missing.push('Flarum app object');
        }

        if (typeof $ === 'undefined') {
            missing.push('jQuery');
        }

        // Check for Swiper
        try {
            // This will be checked when Swiper is actually imported
        } catch {
            missing.push('Swiper library');
        }

        return {
            isValid: missing.length === 0,
            missing
        };
    }

    /**
     * Validate extension configuration
     * @returns Configuration validation results
     */
    validateConfiguration(): ConfigurationCheckResult {
        const issues: string[] = [];
        const config = this.configManager.getExtensionConfig() as Record<string, unknown>;

        if (!config.extensionId) {
            issues.push('Extension ID is not configured');
        }

        const maxSlides = config.maxSlides as number;
        if (maxSlides < ERROR_HANDLING.CONFIG_MAX_SLIDES_MIN || maxSlides > ERROR_HANDLING.CONFIG_MAX_SLIDES_MAX) {
            issues.push(`Max slides should be between ${ERROR_HANDLING.CONFIG_MAX_SLIDES_MIN} and ${ERROR_HANDLING.CONFIG_MAX_SLIDES_MAX}`);
        }

        const transitionTime = config.transitionTime as number;
        if (transitionTime < ERROR_HANDLING.TRANSITION_TIME_MIN || transitionTime > ERROR_HANDLING.TRANSITION_TIME_MAX) {
            issues.push(`Transition time should be between ${ERROR_HANDLING.TRANSITION_TIME_MIN} and ${ERROR_HANDLING.TRANSITION_TIME_MAX} milliseconds`);
        }

        // Note: No slides configured is expected for fresh installations
        // This is not treated as a critical error

        const noIssues = 0;
        return {
            isValid: issues.length === noIssues,
            issues
        };
    }

    /**
     * Initialize error handling for the extension
     * @returns True if initialization successful
     */
    initialize(): boolean {
        try {
            const depCheck = this.checkDependencies();
            if (!depCheck.isValid) {
                this.logError(
                    new Error(`Missing dependencies: ${depCheck.missing.join(', ')}`),
                    'Dependency Check'
                );
                return false;
            }

            const configCheck = this.validateConfiguration();
            if (!configCheck.isValid) {
                this.logError(
                    new Error(`Configuration issues: ${configCheck.issues.join(', ')}`),
                    'Configuration Check'
                );
                // Don't return false for config issues, just log them
            }

            return true;
        } catch (error) {
            this.logError(error as Error, 'Error Handler Initialization');
            return false;
        }
    }
}
