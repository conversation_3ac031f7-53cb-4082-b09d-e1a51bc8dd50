import Swiper from 'swiper';
import { EffectCoverflow, Navigation, Pagination, Autoplay } from 'swiper/modules';
import app from 'flarum/forum/app';
import * as DOMUtils from '../utils/DOMUtils';
import { isMobileDevice } from '../utils/mobile-detection';
import { defaultConfig } from '../../common/config';
import type { FlarumVnode } from '../../common/config/types';

/**
 * Slideshow manager for header advertisements
 */
export class SlideshowManager {
    private swiper: Swiper | null = null;
    private container: HTMLElement | null = null;
    private readonly maxSlides = defaultConfig.slider.maxSlides;
    private readonly checkTime = defaultConfig.slider.checkTime;

    /**
     * Safely read a forum attribute if available
     */
    private getForumAttribute(key: string): unknown {
        try {
            const forum = app?.forum;
            const attrFn = forum?.attribute;
            return typeof attrFn === 'function' ? attrFn.call(forum, key) : undefined;
        } catch {
            return undefined;
        }
    }

    /**
     * Initialize and attach slideshow to the DOM
     */
    attachAdvertiseHeader(vdom: FlarumVnode): void {
        if (isMobileDevice()) {
            this.setupMobileUI();
        }

        this.hideUIElements();
        this.waitForDOMReady(vdom);
    }

    /**
     * Setup mobile-specific UI modifications
     */
    private setupMobileUI(): void {
        const newDiscussionButton = DOMUtils.querySelector(".item-newDiscussion .Button-label");
        if (newDiscussionButton) {
            (newDiscussionButton as HTMLElement).innerHTML = "<div class='buttonRegister'>登录</div>";
            DOMUtils.setStyles(newDiscussionButton as HTMLElement, {
                'display': 'block',
                'font-size': '14px',
                'word-spacing': '-1px'
            });
        }
    }

    /**
     * Hide unnecessary UI elements
     */
    private hideUIElements(): void {
        const iconElement = DOMUtils.querySelector(".item-newDiscussion i");
        if (iconElement) {
            DOMUtils.setStyles(iconElement as HTMLElement, { 'display': 'none' });
        }

        const navElements = DOMUtils.querySelectorAll(".item-nav");
        navElements.forEach(element => {
            DOMUtils.removeElement(element);
        });

        const tagTiles = DOMUtils.querySelector(".TagTiles");
        if (tagTiles) {
            DOMUtils.setStyles(tagTiles as HTMLElement, { 'display': 'none' });
        }
    }

    /**
     * Wait for DOM to be ready and create slideshow
     */
    private waitForDOMReady(vdom: FlarumVnode): void {
        const task = setInterval(() => {
            if (vdom && vdom.dom && document.readyState === 'complete') {
                clearInterval(task);
                this.createSlideshow();
            }
        }, this.checkTime);

        // Add timeout to prevent infinite waiting
        const timeoutDuration = 5000; // 5 second timeout
        setTimeout(() => {
            clearInterval(task);
        }, timeoutDuration);
    }

    /**
     * Create the main slideshow
     */
    private createSlideshow(): void {
        if (DOMUtils.getElementById(defaultConfig.slider.dom.containerId)) {
            return; // Already exists
        }

        const transitionTime = this.getTransitionTime();
        const container = this.createSlideshowContainer();
        const swiper = this.createSwiperElement(container);
        const wrapper = this.createSwiperWrapper(swiper);

        this.populateSlides(wrapper);
        this.addSwiperControls(swiper);
        this.appendToDOM(container);
        this.initializeSwiper(transitionTime);
    }

    /**
     * Get transition time from settings
     * @returns {number} Transition time in milliseconds
     */
    private getTransitionTime(): number {
        const transitionTime = this.getForumAttribute('Client1HeaderAdvTransitionTime');
        return transitionTime ? Number.parseInt(String(transitionTime), 10) : defaultConfig.slider.defaultTransitionTime;
    }

    /**
     * Create slideshow container
     * @returns {HTMLElement} Container element
     */
    private createSlideshowContainer(): HTMLElement {
        const container = DOMUtils.createElement('div', {
            className: 'swiperAdContainer',
            id: defaultConfig.slider.dom.containerId
        });

        if (isMobileDevice()) {
            const screenWidth = globalThis.innerWidth;
            const styleWidth = screenWidth * 2 - 50;
            DOMUtils.setStyles(container, {
                'width': `${styleWidth}px`,
                'margin-left': `${-(styleWidth * 0.254)}px`
            });
        }

        this.container = container;
        return container;
    }

    /**
     * Create Swiper element
     * @param {HTMLElement} container - Parent container
     * @returns {HTMLElement} Swiper element
     */
    private createSwiperElement(container: HTMLElement): HTMLElement {
        const swiper = DOMUtils.createElement('div', {
            className: `swiper ${defaultConfig.slider.dom.swiperClass}`
        });
        DOMUtils.appendChild(container, swiper);
        return swiper;
    }

    /**
     * Create Swiper wrapper
     * @param {HTMLElement} swiper - Swiper element
     * @returns {HTMLElement} Wrapper element
     */
    private createSwiperWrapper(swiper: HTMLElement): HTMLElement {
        const wrapper = DOMUtils.createElement('div', {
            className: 'swiper-wrapper'
        });
        DOMUtils.appendChild(swiper, wrapper);
        return wrapper;
    }

    /**
     * Populate slides with advertisement images
     * @param {HTMLElement} wrapper - Swiper wrapper element
     */
    private populateSlides(wrapper: HTMLElement): void {
        for (let slideIndex = 1; slideIndex <= this.maxSlides; slideIndex += 1) {
            const imageSrc = this.getForumAttribute(`Client1HeaderAdvImage${slideIndex}`);
            const imageLink = this.getForumAttribute(`Client1HeaderAdvLink${slideIndex}`);

            if (imageSrc) {
                const slide = this.createSlide(String(imageSrc), String(imageLink || ''));
                DOMUtils.appendChild(wrapper, slide);
            }
        }
    }

    /**
     * Create individual slide
     * @param {string} imageSrc - Image source URL
     * @param {string} imageLink - Link URL
     * @returns {HTMLElement} Slide element
     */
    private createSlide(imageSrc: string, imageLink: string): HTMLElement {
        const slide = DOMUtils.createElement('div', {
            className: 'swiper-slide'
        });

        const clickHandler = imageLink ? `window.location.href="${imageLink}"` : '';
        slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' />`;

        return slide;
    }

    /**
     * Add Swiper navigation and pagination controls
     * @param {HTMLElement} swiper - Swiper element
     */
    private addSwiperControls(swiper: HTMLElement): void {
        const nextButton = DOMUtils.createElement('div', {
            className: 'swiper-button-next'
        });
        const prevButton = DOMUtils.createElement('div', {
            className: 'swiper-button-prev'
        });
        const pagination = DOMUtils.createElement('div', {
            className: 'swiper-pagination'
        });

        DOMUtils.appendChild(swiper, nextButton);
        DOMUtils.appendChild(swiper, prevButton);
        DOMUtils.appendChild(swiper, pagination);
    }

    /**
     * Append slideshow to DOM
     * @param {HTMLElement} container - Container element
     */
    private appendToDOM(container: HTMLElement): void {
        const contentContainer = DOMUtils.querySelector("#content .container");
        if (contentContainer) {
            DOMUtils.prependChild(contentContainer, container);
        }
    }

    /**
     * Initialize Swiper instance
     * @param {number} transitionTime - Transition time in milliseconds
     */
    private initializeSwiper(transitionTime: number): void {
        try {
            this.swiper = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, {
                autoplay: {
                    delay: transitionTime,
                },
                loop: true,
                spaceBetween: defaultConfig.slider.swiper.spaceBetween,
                effect: defaultConfig.slider.swiper.effect,
                centeredSlides: defaultConfig.slider.swiper.centeredSlides,
                slidesPerView: defaultConfig.slider.swiper.slidesPerView,
                coverflowEffect: {
                    rotate: defaultConfig.slider.swiper.coverflowEffect.rotate,
                    depth: defaultConfig.slider.swiper.coverflowEffect.depth,
                    modifier: defaultConfig.slider.swiper.coverflowEffect.modifier,
                    slideShadows: defaultConfig.slider.swiper.coverflowEffect.slideShadows,
                    stretch: defaultConfig.slider.swiper.coverflowEffect.stretch,
                },
                pagination: {
                    el: defaultConfig.slider.swiper.pagination.el,
                    type: defaultConfig.slider.swiper.pagination.type as 'bullets' | 'fraction' | 'progressbar' | 'custom',
                },
                navigation: {
                    nextEl: defaultConfig.slider.swiper.navigation.nextEl,
                    prevEl: defaultConfig.slider.swiper.navigation.prevEl,
                },
                modules: [EffectCoverflow, Navigation, Pagination, Autoplay]
            });
        } catch {
            // Silently handle Swiper initialization errors
        }
    }

    /**
     * Destroy slideshow instance
     */
    destroy(): void {
        if (this.swiper) {
            this.swiper.destroy(true, true);
            this.swiper = null;
        }

        if (this.container) {
            DOMUtils.removeElement(this.container);
            this.container = null;
        }
    }
}
